import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../models/community_post.dart';
import '../services/community_service.dart';
import '../providers/theme_provider.dart';
import '../utils/logger.dart';

class PostDetailScreen extends StatefulWidget {
  final CommunityPost post;

  const PostDetailScreen({super.key, required this.post});

  @override
  State<PostDetailScreen> createState() => _PostDetailScreenState();
}

class _PostDetailScreenState extends State<PostDetailScreen>
    with TickerProviderStateMixin {
  final TextEditingController _commentController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late AnimationController _likeAnimationController;
  late Animation<double> _likeAnimation;
  bool _isAddingComment = false;
  DateTime? _lastCommentTime;

  // Stream واحد للتعليقات لتجنب التضارب
  late Stream<List<CommunityComment>> _commentsStream;
  List<CommunityComment> _cachedComments = [];
  // متغيرات للـ Pagination
  bool _hasMoreComments = true;
  bool _isLoadingMore = false;

  // متغيرات للردود
  bool _isReplyMode = false;
  CommunityComment? _replyingToComment;
  final FocusNode _commentFocusNode = FocusNode();

  // متغيرات لإظهار/إخفاء الردود
  final Set<String> _expandedComments = <String>{};

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeCommentsStream();

    // انتقال سلس عند فتح الصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          HapticFeedback.selectionClick();
        }
      });
    });
  }

  void _initializeAnimations() {
    _likeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _likeAnimation = Tween<double>(begin: 1.0, end: 1.4).animate(
      CurvedAnimation(
        parent: _likeAnimationController,
        curve: Curves.elasticOut,
      ),
    );
  }

  void _initializeCommentsStream() {
    _commentsStream = CommunityService.getCommentsStream(widget.post.id);

    // الاستماع للتعليقات مع تحسين الأداء
    _commentsStream.listen(
      (comments) {
        if (mounted) {
          // تحديث فقط إذا كانت هناك تغييرات فعلية
          if (_cachedComments.length != comments.length ||
              !_areCommentsEqual(_cachedComments, comments)) {
            setState(() {
              _cachedComments = comments;
            });
          }
        }
      },
      onError: (error) {
        AppLogger.error('خطأ في تحميل التعليقات', 'PostDetailScreen', error);
      },
    );
  }

  bool _areCommentsEqual(
    List<CommunityComment> list1,
    List<CommunityComment> list2,
  ) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i].id != list2[i].id ||
          list1[i].likedBy.length != list2[i].likedBy.length) {
        return false;
      }
    }
    return true;
  }

  Future<void> _loadMoreComments() async {
    if (_isLoadingMore || !_hasMoreComments) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      // هنا يمكن إضافة استدعاء API لتحميل المزيد
      // مؤقتاً سنحاكي التحميل
      await Future.delayed(const Duration(seconds: 1));

      // إذا لم تعد هناك تعليقات أخرى
      setState(() {
        _hasMoreComments = false;
      });
    } catch (e) {
      AppLogger.error(
        'خطأ في تحميل المزيد من التعليقات',
        'PostDetailScreen',
        e,
      );
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  void dispose() {
    _commentController.dispose();
    _scrollController.dispose();
    _likeAnimationController.dispose();
    _commentFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          appBar: _buildAppBar(themeProvider),
          body: Column(
            children: [
              // المنشور الأساسي
              Expanded(
                child: CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                    // المنشور
                    SliverToBoxAdapter(child: _buildPostCard(themeProvider)),

                    // قسم التعليقات
                    SliverToBoxAdapter(
                      child: _buildCommentsHeader(themeProvider),
                    ),

                    // قائمة التعليقات
                    _buildCommentsList(themeProvider),
                  ],
                ),
              ),

              // شريط إضافة التعليق
              _buildCommentInput(themeProvider),
            ],
          ),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeProvider themeProvider) {
    return AppBar(
      backgroundColor:
          themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back_ios_rounded,
          color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'المنشور',
        style: GoogleFonts.cairo(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(
            Icons.more_vert_rounded,
            color: themeProvider.isDarkMode ? Colors.white70 : Colors.black54,
          ),
          onPressed: () => _showPostOptions(),
        ),
      ],
    );
  }

  Widget _buildPostCard(ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس المنشور
          _buildPostHeader(themeProvider),

          // محتوى المنشور
          _buildPostContent(themeProvider),

          // أزرار التفاعل
          _buildActionButtons(themeProvider),
        ],
      ),
    );
  }

  Widget _buildPostHeader(ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // صورة المؤلف
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [const Color(0xFF6366F1), const Color(0xFF8B5CF6)],
              ),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Center(
              child: Text(
                widget.post.authorName.isNotEmpty
                    ? widget.post.authorName[0].toUpperCase()
                    : 'م',
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),

          // معلومات المؤلف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.post.authorName,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white
                            : Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatTimestamp(widget.post.timestamp),
                  style: GoogleFonts.cairo(
                    fontSize: 13,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.white60
                            : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostContent(ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // النص
          Text(
            widget.post.content,
            style: GoogleFonts.cairo(
              fontSize: 15,
              height: 1.6,
              color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
            ),
          ),

          // الاستطلاع إذا كان موجوداً
          if (widget.post.poll != null) ...[
            const SizedBox(height: 16),
            _buildPollWidget(themeProvider),
          ],

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildPollWidget(ThemeProvider themeProvider) {
    final poll = widget.post.poll!;
    final options = List<String>.from(poll['options'] ?? []);
    final votes = Map<String, int>.from(poll['votes'] ?? {});
    final totalVotes = votes.values.fold(0, (total, count) => total + count);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode
                ? const Color(0xFF334155)
                : const Color(0xFFF1F5F9),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.poll_rounded,
                color: const Color(0xFF6366F1),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'استطلاع رأي',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF6366F1),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // خيارات الاستطلاع
          ...options.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final voteCount = votes[index.toString()] ?? 0;
            final percentage = totalVotes > 0 ? (voteCount / totalVotes) : 0.0;

            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: InkWell(
                onTap: () => _voteInPoll(index),
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFF475569)
                            : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color:
                          themeProvider.isDarkMode
                              ? const Color(0xFF64748B)
                              : const Color(0xFFE2E8F0),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          option,
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color:
                                themeProvider.isDarkMode
                                    ? Colors.white
                                    : Colors.black87,
                          ),
                        ),
                      ),
                      Text(
                        '${(percentage * 100).toInt()}%',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF6366F1),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),

          if (totalVotes > 0) ...[
            const SizedBox(height: 8),
            Text(
              '$totalVotes صوت',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color:
                    themeProvider.isDarkMode
                        ? Colors.white60
                        : Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons(ThemeProvider themeProvider) {
    final isLiked = widget.post.likedBy.contains(
      CommunityService.currentUserId,
    );

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: Row(
        children: [
          // زر الإعجاب
          Expanded(
            child: _buildActionButton(
              icon: isLiked ? Icons.favorite : Icons.favorite_border,
              label: '${widget.post.likedBy.length}',
              color: isLiked ? const Color(0xFFFF3040) : null,
              onTap: _toggleLike,
              themeProvider: themeProvider,
              isActive: isLiked,
            ),
          ),

          // زر التعليق
          Expanded(
            child: _buildActionButton(
              icon: Icons.chat_bubble_outline,
              label: '${_cachedComments.length}',
              onTap: () => _scrollToComments(),
              themeProvider: themeProvider,
              color: const Color(0xFF1DA1F2),
            ),
          ),

          // زر المشاركة
          Expanded(
            child: _buildActionButton(
              icon: Icons.share_outlined,
              label: 'مشاركة',
              onTap: _sharePost,
              themeProvider: themeProvider,
              color: const Color(0xFF10B981),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required ThemeProvider themeProvider,
    Color? color,
    bool isActive = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedBuilder(
              animation: _likeAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: icon == Icons.favorite ? _likeAnimation.value : 1.0,
                  child: Icon(
                    icon,
                    size: 20,
                    color:
                        color ??
                        (themeProvider.isDarkMode
                            ? Colors.white70
                            : Colors.grey[600]),
                  ),
                );
              },
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color:
                    color ??
                    (themeProvider.isDarkMode
                        ? Colors.white70
                        : Colors.grey[600]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentsHeader(ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.chat_bubble_outline,
            color: const Color(0xFF6366F1),
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'التعليقات',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${_cachedComments.length}',
              style: GoogleFonts.cairo(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF6366F1),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentsList(ThemeProvider themeProvider) {
    if (_cachedComments.isEmpty) {
      return SliverToBoxAdapter(child: _buildEmptyComments(themeProvider));
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        // إضافة زر "تحميل المزيد" في النهاية
        if (index == _cachedComments.length) {
          return _buildLoadMoreButton(themeProvider);
        }

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: _buildCommentItem(_cachedComments[index], themeProvider),
        );
      }, childCount: _cachedComments.length + (_hasMoreComments ? 1 : 0)),
    );
  }

  Widget _buildLoadMoreButton(ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.all(16),
      child:
          _isLoadingMore
              ? const Center(child: CircularProgressIndicator())
              : ElevatedButton(
                onPressed: _loadMoreComments,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6366F1),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'تحميل المزيد من التعليقات',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
    );
  }

  Widget _buildEmptyComments(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 48,
            color: themeProvider.isDarkMode ? Colors.white30 : Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد تعليقات بعد',
            style: GoogleFonts.cairo(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color:
                  themeProvider.isDarkMode ? Colors.white60 : Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'كن أول من يعلق على هذا المنشور',
            style: GoogleFonts.cairo(
              fontSize: 14,
              color:
                  themeProvider.isDarkMode ? Colors.white38 : Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCommentItem(
    CommunityComment comment,
    ThemeProvider themeProvider, {
    bool isReply = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // التعليق الرئيسي
        _buildSingleComment(comment, themeProvider, isReply: isReply),

        // رابط عرض الردود (إذا كانت موجودة ومخفية)
        if (!isReply && comment.replies.isNotEmpty) ...[
          const SizedBox(height: 6),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.only(right: 60, left: 16),
            child: GestureDetector(
              onTap: () => _toggleRepliesVisibility(comment.id),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 12,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.grey[300]!, width: 1),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 16,
                      height: 1.5,
                      decoration: BoxDecoration(
                        color: Colors.grey[400],
                        borderRadius: BorderRadius.circular(1),
                      ),
                      margin: const EdgeInsets.only(left: 8),
                    ),
                    Text(
                      _expandedComments.contains(comment.id)
                          ? 'إخفاء الردود'
                          : 'عرض ${comment.replies.length == 1 ? 'رد واحد' : '${comment.replies.length} ردود'}',
                      style: GoogleFonts.cairo(
                        fontSize: 13,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      _expandedComments.contains(comment.id)
                          ? Icons.keyboard_arrow_up_rounded
                          : Icons.keyboard_arrow_down_rounded,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],

        // الردود (إذا كانت مرئية)
        AnimatedSize(
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeOutCubic,
          child:
              !isReply &&
                      comment.replies.isNotEmpty &&
                      _expandedComments.contains(comment.id)
                  ? Column(
                    children: [
                      const SizedBox(height: 12),
                      ...comment.replies.map(
                        (reply) => AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOutBack,
                          margin: const EdgeInsets.only(right: 48, bottom: 8),
                          child: _buildSingleComment(
                            reply,
                            themeProvider,
                            isReply: true,
                          ),
                        ),
                      ),
                    ],
                  )
                  : const SizedBox.shrink(),
        ),
      ],
    );
  }

  Widget _buildSingleComment(
    CommunityComment comment,
    ThemeProvider themeProvider, {
    bool isReply = false,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6, horizontal: isReply ? 8 : 0),
      child: GestureDetector(
        onLongPress: () => _showDeleteCommentDialog(comment),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // صورة المعلق (على اليسار)
              Container(
                width: isReply ? 28 : 32,
                height: isReply ? 28 : 32,
                decoration: BoxDecoration(
                  color: isReply ? Colors.grey[200] : Colors.grey[300],
                  borderRadius: BorderRadius.circular(isReply ? 14 : 16),
                  border:
                      isReply
                          ? Border.all(color: Colors.grey[300]!, width: 1)
                          : null,
                ),
                child: Center(
                  child: Text(
                    comment.authorName.isNotEmpty
                        ? comment.authorName[0].toUpperCase()
                        : 'م',
                    style: GoogleFonts.cairo(
                      color: Colors.grey[700],
                      fontSize: isReply ? 11 : 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // محتوى التعليق
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // النص الرئيسي
                    RichText(
                      text: TextSpan(
                        children: [
                          // اسم المستخدم
                          TextSpan(
                            text: comment.authorName,
                            style: GoogleFonts.cairo(
                              fontSize: isReply ? 13 : 14,
                              fontWeight: FontWeight.bold,
                              color:
                                  themeProvider.isDarkMode
                                      ? Colors.white
                                      : Colors.black87,
                            ),
                          ),
                          const TextSpan(text: ' '),
                          // محتوى التعليق
                          TextSpan(
                            text: comment.content,
                            style: GoogleFonts.cairo(
                              fontSize: isReply ? 13 : 14,
                              color:
                                  themeProvider.isDarkMode
                                      ? Colors.white
                                      : Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 8),

                    // أزرار التفاعل
                    Row(
                      children: [
                        // الوقت
                        Text(
                          _formatTimestamp(comment.timestamp),
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),

                        const SizedBox(width: 16),

                        // زر الرد
                        if (!isReply) ...[
                          GestureDetector(
                            onTap: () => _replyToComment(comment),
                            child: Text(
                              'رد',
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 8),

              // قسم الإعجاب (على اليمين)
              Column(
                children: [
                  // زر القلب
                  GestureDetector(
                    onTap: () => _toggleCommentLike(comment),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color:
                            comment.likedBy.contains(
                                  CommunityService.currentUserId,
                                )
                                ? Colors.red.withValues(alpha: 0.1)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        comment.likedBy.contains(CommunityService.currentUserId)
                            ? Icons.favorite
                            : Icons.favorite_border,
                        size: 18,
                        color:
                            comment.likedBy.contains(
                                  CommunityService.currentUserId,
                                )
                                ? Colors.red
                                : Colors.grey[600],
                      ),
                    ),
                  ),

                  // عدد الإعجابات
                  if (comment.likedBy.isNotEmpty) ...[
                    const SizedBox(height: 2),
                    Text(
                      '${comment.likedBy.length}',
                      style: GoogleFonts.cairo(
                        fontSize: 11,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCommentInput(ThemeProvider themeProvider) {
    return Container(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 16,
        bottom: MediaQuery.of(context).viewInsets.bottom + 16,
      ),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // شريط الرد (إذا كان في وضع الرد)
          if (_isReplyMode && _replyingToComment != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF6366F1).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFF6366F1).withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.reply, size: 16, color: const Color(0xFF6366F1)),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'الرد على ${_replyingToComment!.authorName}',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: const Color(0xFF6366F1),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: _cancelReply,
                    icon: const Icon(
                      Icons.close,
                      size: 16,
                      color: Color(0xFF6366F1),
                    ),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],

          // صف الإدخال
          Row(
            children: [
              // صورة المستخدم
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                  ),
                  borderRadius: BorderRadius.circular(18),
                ),
                child: Center(
                  child: Text(
                    CommunityService.currentUserName.isNotEmpty
                        ? CommunityService.currentUserName[0].toUpperCase()
                        : 'م',
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // حقل النص
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFF334155)
                            : const Color(0xFFF1F5F9),
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(
                      color:
                          themeProvider.isDarkMode
                              ? const Color(0xFF475569)
                              : const Color(0xFFE2E8F0),
                    ),
                  ),
                  child: TextField(
                    controller: _commentController,
                    focusNode: _commentFocusNode,
                    decoration: InputDecoration(
                      hintText:
                          _isReplyMode ? 'اكتب رداً...' : 'اكتب تعليقاً...',
                      hintStyle: GoogleFonts.cairo(
                        fontSize: 14,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white60
                                : Colors.grey[600],
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color:
                          themeProvider.isDarkMode
                              ? Colors.white
                              : Colors.black87,
                    ),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _addComment(),
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // زر الإرسال
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  color:
                      _isAddingComment
                          ? const Color(0xFF6366F1).withValues(alpha: 0.7)
                          : const Color(0xFF6366F1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  onPressed: _isAddingComment ? null : _addComment,
                  icon:
                      _isAddingComment
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : const Icon(
                            Icons.send_rounded,
                            color: Colors.white,
                            size: 20,
                          ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // دوال التفاعل
  Future<void> _toggleLike() async {
    final isCurrentlyLiked = widget.post.likedBy.contains(
      CommunityService.currentUserId,
    );

    // تحديث فوري للواجهة
    setState(() {
      if (isCurrentlyLiked) {
        widget.post.likedBy.remove(CommunityService.currentUserId);
      } else {
        widget.post.likedBy.add(CommunityService.currentUserId);
        _likeAnimationController.forward().then((_) {
          _likeAnimationController.reverse();
        });
        HapticFeedback.lightImpact();
      }
    });

    try {
      final success = await CommunityService.toggleLike(widget.post.id);
      if (!success) {
        // إعادة الحالة السابقة في حالة الفشل
        setState(() {
          if (isCurrentlyLiked) {
            widget.post.likedBy.add(CommunityService.currentUserId);
          } else {
            widget.post.likedBy.remove(CommunityService.currentUserId);
          }
        });
      }
    } catch (e) {
      // إعادة الحالة السابقة في حالة الخطأ
      setState(() {
        if (isCurrentlyLiked) {
          widget.post.likedBy.add(CommunityService.currentUserId);
        } else {
          widget.post.likedBy.remove(CommunityService.currentUserId);
        }
      });
    }
  }

  Future<void> _addComment() async {
    final content = _commentController.text.trim();
    if (content.isEmpty) return;

    // منع الضغط المتكرر
    final now = DateTime.now();
    if (_lastCommentTime != null &&
        now.difference(_lastCommentTime!).inSeconds < 2) {
      return;
    }
    _lastCommentTime = now;

    if (_isAddingComment) return;

    setState(() {
      _isAddingComment = true;
    });

    // إنشاء تعليق مؤقت لإظهاره فوراً
    final tempComment = CommunityComment(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      postId: widget.post.id,
      authorId: CommunityService.currentUserId,
      authorName: CommunityService.currentUserName,
      content: content,
      timestamp: DateTime.now(),
      parentCommentId: _isReplyMode ? _replyingToComment?.id : null,
      replyToUserId: _isReplyMode ? _replyingToComment?.authorId : null,
      replyToUserName: _isReplyMode ? _replyingToComment?.authorName : null,
    );

    // إضافة التعليق للقائمة فوراً
    setState(() {
      if (_isReplyMode && _replyingToComment != null) {
        // إضافة رد
        final parentIndex = _cachedComments.indexWhere(
          (c) => c.id == _replyingToComment!.id,
        );
        if (parentIndex != -1) {
          _cachedComments[parentIndex].replies.add(tempComment);
          _expandedComments.add(_replyingToComment!.id);
        }
      } else {
        // إضافة تعليق جديد في المقدمة
        _cachedComments.insert(0, tempComment);
      }
    });

    _commentController.clear();
    _cancelReply();
    HapticFeedback.lightImpact();

    try {
      final success = await CommunityService.addComment(
        postId: widget.post.id,
        content: content,
        parentCommentId: _isReplyMode ? _replyingToComment?.id : null,
        replyToUserId: _isReplyMode ? _replyingToComment?.authorId : null,
        replyToUserName: _isReplyMode ? _replyingToComment?.authorName : null,
      );

      if (success == null) {
        // إزالة التعليق المؤقت في حالة الفشل
        setState(() {
          if (_isReplyMode && _replyingToComment != null) {
            final parentIndex = _cachedComments.indexWhere(
              (c) => c.id == _replyingToComment!.id,
            );
            if (parentIndex != -1) {
              _cachedComments[parentIndex].replies.removeWhere(
                (c) => c.id == tempComment.id,
              );
            }
          } else {
            _cachedComments.removeWhere((c) => c.id == tempComment.id);
          }
        });
        throw Exception('فشل في إضافة التعليق');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة التعليق', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
      AppLogger.error('خطأ في إضافة التعليق', 'PostDetailScreen', e);
    } finally {
      if (mounted) {
        setState(() {
          _isAddingComment = false;
        });
      }
    }
  }

  void _scrollToComments() {
    HapticFeedback.selectionClick();

    // انتقال سلس ومريح للتعليقات
    _scrollController.animateTo(
      _scrollController.position.maxScrollExtent,
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeOutCubic,
    );

    // تأثير بصري لطيف
    _commentFocusNode.requestFocus();
  }

  void _sharePost() {
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم نسخ رابط المنشور', style: GoogleFonts.cairo()),
        backgroundColor: const Color(0xFF10B981),
      ),
    );
  }

  Future<void> _voteInPoll(int optionIndex) async {
    try {
      await CommunityService.voteInPoll(
        postId: widget.post.id,
        optionIndex: optionIndex,
      );
      HapticFeedback.lightImpact();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التصويت', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleCommentLike(CommunityComment comment) async {
    if (!mounted) return;

    try {
      HapticFeedback.lightImpact();

      // تحديث الواجهة فوراً للاستجابة السريعة
      if (mounted) {
        setState(() {
          if (comment.likedBy.contains(CommunityService.currentUserId)) {
            comment.likedBy.remove(CommunityService.currentUserId);
          } else {
            comment.likedBy.add(CommunityService.currentUserId);
          }
        });
      }

      // إرسال التحديث للخادم
      final success = await CommunityService.toggleCommentLike(comment.id);

      if (!success && mounted) {
        // إذا فشل، أعد الحالة كما كانت
        setState(() {
          if (comment.likedBy.contains(CommunityService.currentUserId)) {
            comment.likedBy.remove(CommunityService.currentUserId);
          } else {
            comment.likedBy.add(CommunityService.currentUserId);
          }
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في تحديث الإعجاب', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في تبديل إعجاب التعليق', 'PostDetailScreen', e);
    }
  }

  void _showDeleteCommentDialog(CommunityComment comment) {
    // التحقق من الصلاحيات
    final currentUserId = CommunityService.currentUserId;
    final currentUserEmail = CommunityService.currentUser?.email;
    final isOwner = comment.authorId == currentUserId;
    final isAdmin = currentUserEmail == '<EMAIL>';

    if (!isOwner && !isAdmin) {
      return; // لا توجد صلاحية
    }

    HapticFeedback.mediumImpact();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'حذف التعليق',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: Text(
              'هل أنت متأكد من حذف هذا التعليق؟ لا يمكن التراجع عن هذا الإجراء.',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'إلغاء',
                  style: GoogleFonts.cairo(color: Colors.grey[600]),
                ),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _deleteComment(comment);
                },
                child: Text(
                  'حذف',
                  style: GoogleFonts.cairo(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  Future<void> _deleteComment(CommunityComment comment) async {
    try {
      HapticFeedback.heavyImpact();

      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      final success = await CommunityService.deleteComment(comment.id);

      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم حذف التعليق بنجاح', style: GoogleFonts.cairo()),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في حذف التعليق', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'حدث خطأ أثناء حذف التعليق',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
      AppLogger.error('خطأ في حذف التعليق', 'PostDetailScreen', e);
    }
  }

  void _replyToComment(CommunityComment comment) {
    setState(() {
      _isReplyMode = true;
      _replyingToComment = comment;
    });

    // التركيز على حقل النص
    _commentFocusNode.requestFocus();

    // إضافة نص افتراضي للرد
    _commentController.text = '@${comment.authorName} ';
    _commentController.selection = TextSelection.fromPosition(
      TextPosition(offset: _commentController.text.length),
    );

    HapticFeedback.lightImpact();
  }

  void _cancelReply() {
    setState(() {
      _isReplyMode = false;
      _replyingToComment = null;
    });
    _commentController.clear();
  }

  void _toggleRepliesVisibility(String commentId) {
    setState(() {
      if (_expandedComments.contains(commentId)) {
        _expandedComments.remove(commentId);
        HapticFeedback.selectionClick();
      } else {
        _expandedComments.add(commentId);
        HapticFeedback.lightImpact();
      }
    });

    // انتقال سلس للردود الجديدة
    Future.delayed(const Duration(milliseconds: 200), () {
      if (_expandedComments.contains(commentId)) {
        _scrollController.animateTo(
          _scrollController.offset + 100,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
        );
      }
    });
  }

  void _showPostOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(top: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                ListTile(
                  leading: const Icon(Icons.share),
                  title: Text('مشاركة', style: GoogleFonts.cairo()),
                  onTap: () {
                    Navigator.pop(context);
                    _sharePost();
                  },
                ),
                if (widget.post.authorId == CommunityService.currentUserId)
                  ListTile(
                    leading: const Icon(Icons.delete, color: Colors.red),
                    title: Text(
                      'حذف',
                      style: GoogleFonts.cairo(color: Colors.red),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: تنفيذ حذف المنشور
                    },
                  ),
                ListTile(
                  leading: const Icon(Icons.report),
                  title: Text('إبلاغ', style: GoogleFonts.cairo()),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: تنفيذ الإبلاغ
                  },
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} د';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} س';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} ي';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}
